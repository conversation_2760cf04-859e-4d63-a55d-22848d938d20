import React, { useEffect, useState } from 'react';
import { Spin } from 'antd';
import { useWallet } from '@solana/wallet-adapter-react';
import { WalletMultiButton } from '@solana/wallet-adapter-react-ui';
import { useModel } from '@umijs/max';

const GlobalHeaderInfo: React.FC = () => {
  const [time, setTime] = useState<string>(new Date().toLocaleString());
  const { connected, publicKey } = useWallet();
  const { totalBalance, loading, refreshMultisigs } = useModel('multisig');

  useEffect(() => {
    const timer = setInterval(() => {
      setTime(new Date().toLocaleString());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  // 组件挂载时加载数据
  useEffect(() => {
    refreshMultisigs();
  }, []);

  return (
    <div style={{
      marginBottom: 16,
      padding: '12px 16px',
      background: '#fff',
      borderRadius: '6px',
      boxShadow: '0 1px 2px rgba(0, 0, 0, 0.1)',
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center'
    }}>
      <div style={{ fontWeight: 500, color: '#666' }}>
        {time}
      </div>

      <div style={{
        display: 'flex',
        alignItems: 'center',
        gap: '12px'
      }}>
        {connected && publicKey && (
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            padding: '4px 8px',
            backgroundColor: '#f0f2f5',
            borderRadius: '6px'
          }}>
            <div style={{ fontSize: '16px', fontWeight: '600', lineHeight: 1.6, color: '#52c41a' }}>
              {loading ? (
                <Spin size="small" />
              ) : (
                `${totalBalance.toFixed(4)} SOL`
              )}
            </div>
          </div>
        )}
        <WalletMultiButton
          style={{
            height: '36px',
            fontSize: '14px',
            borderRadius: '6px'
          }}
        />
      </div>
    </div>
  );
};

export default GlobalHeaderInfo;
