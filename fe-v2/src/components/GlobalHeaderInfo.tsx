import React, { useEffect, useState } from 'react';
import { Spin, Button, Space } from 'antd';
import { SyncOutlined, DollarOutlined } from '@ant-design/icons';
import { useWallet } from '@solana/wallet-adapter-react';
import { WalletMultiButton } from '@solana/wallet-adapter-react-ui';
import { useModel } from '@umijs/max';
import { formatUsdAmount } from '@/utils/price';

const GlobalHeaderInfo: React.FC = () => {
  const { connected, publicKey } = useWallet();
  const { userBalance, totalVaultValue, loading, refreshMultisigs } = useModel('multisig');
  const [refreshing, setRefreshing] = useState(false);

  // 组件挂载时加载数据（只在应用启动时加载一次）
  useEffect(() => {
    refreshMultisigs(false, 'data-required');
  }, []);

  // 处理刷新按钮点击
  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await refreshMultisigs(true); // 强制刷新
    } finally {
      setRefreshing(false);
    }
  };

  return (
    <div style={{
      marginBottom: 16,
      padding: '12px 16px',
      background: '#fff',
      borderRadius: '6px',
      boxShadow: '0 1px 2px rgba(0, 0, 0, 0.1)',
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center'
    }}>
      <div style={{
        display: 'flex',
        alignItems: 'center',
        gap: '12px',
        fontWeight: 500,
        color: '#666'
      }}>
        <Space>
          <Button
            type="primary"
            size="small"
            loading={refreshing}
            onClick={handleRefresh}
            title="刷新数据"
          >
            刷新
          </Button>
          <DollarOutlined style={{ color: '#52c41a' }} />
          <span style={{ fontSize: '16px', fontWeight: '600', color: '#52c41a' }}>
            {loading ? (
              <Spin size="small" />
            ) : (
              `金库总价值: ${formatUsdAmount(totalVaultValue)}`
            )}
          </span>

        </Space>
      </div>

      <div style={{
        display: 'flex',
        alignItems: 'center',
        gap: '12px'
      }}>
        {connected && publicKey && (
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            padding: '4px 8px',
            backgroundColor: '#f0f2f5',
            borderRadius: '6px'
          }}>
            <div style={{ fontSize: '16px', fontWeight: '600', lineHeight: 1.6, color: '#52c41a' }}>
              {loading ? (
                <Spin size="small" />
              ) : (
                `${userBalance.toFixed(5)} SOL`
              )}
            </div>
          </div>
        )}
        <WalletMultiButton
          style={{
            height: '36px',
            fontSize: '14px',
            borderRadius: '6px'
          }}
        />
      </div>
    </div>
  );
};

export default GlobalHeaderInfo;
