import React from 'react';
import { Button, message, Space } from 'antd';
import { CopyOutlined } from '@ant-design/icons';

interface AddressCopyProps {
  address: string;
  label?: string;
  displayLength?: number;
  showFullAddress?: boolean;
  size?: 'small' | 'middle' | 'large';
  type?: 'default' | 'primary' | 'ghost' | 'dashed' | 'link' | 'text';
}

const AddressCopy: React.FC<AddressCopyProps> = ({
  address,
  label = '地址',
  displayLength = 12,
  showFullAddress = false,
  size = 'small',
  type = 'link'
}) => {
  const formatAddress = (addr: string, length: number = 12) => {
    if (!addr) return '';
    if (showFullAddress) return addr;
    return `${addr.substring(0, length)}...${addr.substring(addr.length - 4)}`;
  };

  const copyToClipboard = async (text: string, labelText: string) => {
    if (!text) return;
    
    try {
      await navigator.clipboard.writeText(text);
      message.success(`${labelText}已复制到剪贴板`);
    } catch (err) {
      // 降级方案
      const textArea = document.createElement('textarea');
      textArea.value = text;
      document.body.appendChild(textArea);
      textArea.select();
      try {
        document.execCommand('copy');
        message.success(`${labelText}已复制到剪贴板`);
      } catch (fallbackErr) {
        message.error('复制失败');
      }
      document.body.removeChild(textArea);
    }
  };

  return (
    <Space>
      <code style={{ fontSize: showFullAddress ? '12px' : '14px' }}>
        {formatAddress(address, displayLength)}
      </code>
      <Button
        type={type}
        size={size}
        icon={<CopyOutlined />}
        onClick={() => copyToClipboard(address, label)}
      >
        复制
      </Button>
    </Space>
  );
};

export default AddressCopy;
