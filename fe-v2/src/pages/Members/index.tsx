import React, { useEffect } from 'react';
import { Card, Space, Tag, Row, Col, Typography, Divider, Spin } from 'antd';
import { useWallet } from '@solana/wallet-adapter-react';
import { UserOutlined, SafetyOutlined } from '@ant-design/icons';
import { useModel } from '@umijs/max';
import AddressCopy from '@/components/AddressCopy';
import { parsePermissions, getPermissionLabels, getPermissionColor } from '@/utils/permissions';

const { Text } = Typography;

const MembersPage: React.FC = () => {
  const { publicKey } = useWallet();
  const { currentMultisig, loading, refreshMultisigs } = useModel('multisig');

  // 页面加载时不需要重新请求数据（使用缓存）
  useEffect(() => {
    refreshMultisigs(false, 'data-optional');
  }, []);

  const isCurrentUser = (memberKey: string) => {
    return publicKey?.toBase58() === memberKey;
  };

  // Loading 状态
  if (loading) {
    return (
      <div style={{ padding: 24 }}>
        <Card>
          <div style={{ textAlign: 'center', padding: '60px 0' }}>
            <Spin size="large" />
            <p style={{ color: '#999', margin: '16px 0 0 0' }}>加载成员信息中...</p>
          </div>
        </Card>
      </div>
    );
  }

  // 无数据状态
  if (!currentMultisig) {
    return (
      <div style={{ padding: 24 }}>
        <Card>
          <div style={{ textAlign: 'center', padding: '60px 0' }}>
            <p style={{ color: '#999', margin: 0 }}>未找到多签账户配置</p>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div>
      <Row gutter={[16, 16]}>
        {currentMultisig.members.map((member) => {
          const permissions = parsePermissions(member.permissions);
          const permissionLabels = getPermissionLabels(permissions);
          return (
            <Col span={8} key={member.key}>
              <Card
                size="small"
                style={{
                  backgroundColor: isCurrentUser(member.key) ? '#f0f8ff' : '#fff',
                  border: isCurrentUser(member.key) ? '1px solid #1890ff' : '1px solid #d9d9d9',
                  borderRadius: 8,
                }}
              >
                <Space direction="vertical" size="small" style={{ width: '100%' }}>
                  <div style={{ textAlign: 'center' }}>
                    <Space direction="vertical" size={4}>
                      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 8 }}>
                      <UserOutlined style={{ color: '#1890ff', fontSize: 16 }} />
                        <Text strong style={{ fontSize: 14 }}>成员</Text>
                      </div>
                      {isCurrentUser(member.key) && (<Tag color="purple">当前用户</Tag>)}
                    </Space>
                  </div>
                  <Divider style={{ margin: '8px 0' }} />
                  <div style={{ textAlign: 'center' }}>
                    <AddressCopy address={member.key} label="地址" showFullAddress={true} size="small" type="link" />
                  </div>
                  <Divider style={{ margin: '8px 0' }} />
                  <div style={{ textAlign: 'center' }}>
                    <div style={{ marginBottom: 8 }}>
                      <SafetyOutlined style={{ color: '#52c41a', marginRight: 4 }} />
                      <Text type="secondary" style={{ fontSize: 12 }}>权限</Text>
                    </div>
                    <Space wrap size={[4, 4]} style={{ justifyContent: 'center' }}>
                      {permissionLabels.map((label) => (
                        <Tag key={label} color={getPermissionColor(label)} style={{ margin: 0, fontSize: 11, padding: '2px 6px', borderRadius: 4 }}>
                          {label}
                        </Tag>
                      ))}
                    </Space>
                  </div>
                </Space>
              </Card>
            </Col>
          );
        })}
      </Row>
    </div>
  );
};

export default MembersPage;