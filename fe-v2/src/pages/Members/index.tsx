import React from 'react';
import { <PERSON>, Space, Badge, Row, Col } from 'antd';
import { useWallet } from '@solana/wallet-adapter-react';
import { CrownOutlined, UserOutlined } from '@ant-design/icons';
import { useModel } from '@umijs/max';
import AddressCopy from '@/components/AddressCopy';

const MembersPage: React.FC = () => {
  const { publicKey } = useWallet();
  const { currentMultisig } = useModel('multisig');

  const isCurrentUser = (memberKey: string) => {
    return publicKey?.toBase58() === memberKey;
  };

  const isCreator = (memberKey: string) => {
    return currentMultisig?.createKey === memberKey;
  };

  if (!currentMultisig) {
    return (
      <div style={{ padding: 24 }}>
        <Card>
          <div style={{ textAlign: 'center', padding: '60px 0' }}>
            <p style={{ color: '#999', margin: 0 }}>未找到多签账户配置</p>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div style={{ padding: 24 }}>
      <Row gutter={[16, 16]}>
        {currentMultisig.members.map((member) => (
          <Col xs={24} sm={12} md={8} lg={6} key={member.key}>
            <Card
              size="small"
              style={{
                backgroundColor: isCurrentUser(member.key) ? '#f0f8ff' : '#fff',
                border: isCurrentUser(member.key) ? '2px solid #1890ff' : '1px solid #d9d9d9',
              }}
            >
              <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                {/* 上方：权限信息 */}
                <div style={{ textAlign: 'center' }}>
                  <Space>
                    {isCreator(member.key) ? (
                      <>
                        <CrownOutlined style={{ color: '#faad14', fontSize: 16 }} />
                        <Badge count="创建者" style={{ backgroundColor: '#52c41a' }} />
                      </>
                    ) : (
                      <>
                        <UserOutlined style={{ color: '#1890ff', fontSize: 16 }} />
                        <Badge count="成员" style={{ backgroundColor: '#1890ff' }} />
                      </>
                    )}
                  </Space>
                  {isCurrentUser(member.key) && (
                    <div style={{ marginTop: 8 }}>
                      <Badge count="当前用户" style={{ backgroundColor: '#722ed1' }} />
                    </div>
                  )}
                </div>

                {/* 下方：完整地址和复制功能 */}
                <div style={{ textAlign: 'center' }}>
                  <AddressCopy
                    address={member.key}
                    label="成员地址"
                    showFullAddress={true}
                    size="small"
                    type="link"
                  />
                </div>
              </Space>
            </Card>
          </Col>
        ))}
      </Row>
    </div>
  );
};

export default MembersPage;