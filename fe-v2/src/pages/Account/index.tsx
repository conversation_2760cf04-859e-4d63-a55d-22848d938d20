import React, { useEffect, useState } from 'react';
import { Table, Avatar, Button, message, Typography, Space } from 'antd';
import { CopyOutlined } from '@ant-design/icons';
import { useWallet } from '@solana/wallet-adapter-react';
import { useModel } from '@umijs/max';

const { Text } = Typography;

interface CoinAsset {
  symbol: string;
  icon: string;
  address: string;
  balance: number;
  value: number;
  weight: number;
}

const shortAddr = (addr: string) => addr.slice(0, 4) + '...' + addr.slice(-4);

// Token 辅助函数
const getTokenSymbol = (mint: string) => {
  const tokenSymbols: { [key: string]: string } = {
    'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v': 'USDC',
    'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB': 'USDT',
    'So11111111111111111111111111111111111111112': 'SOL',
    'mSoLzYCxHdYgdzU16g5QSh3i5K3z3KZK7ytfqcJm7So': 'mSOL',
  };
  return tokenSymbols[mint] || `${mint.substring(0, 4)}...${mint.substring(mint.length - 4)}`;
};

const getTokenIcon = (mint: string) => {
  const tokenIcons: { [key: string]: string } = {
    'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v': 'https://cryptologos.cc/logos/usd-coin-usdc-logo.png',
    'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB': 'https://cryptologos.cc/logos/tether-usdt-logo.png',
    'So11111111111111111111111111111111111111112': 'https://cryptologos.cc/logos/solana-sol-logo.png',
  };
  return tokenIcons[mint] || 'https://cryptologos.cc/logos/solana-sol-logo.png';
};

const getTokenPrice = (mint: string) => {
  const tokenPrices: { [key: string]: number } = {
    'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v': 1, // USDC
    'Es9vMFrzaCERmJfrF4H2FYD4KCoNkY11McCe8BenwNYB': 1, // USDT
    'So11111111111111111111111111111111111111112': 200, // SOL
  };
  return tokenPrices[mint] || 0;
};

const AccountPage: React.FC = () => {
  const { connected } = useWallet();
  const { multisigs, loading, refreshMultisigs } = useModel('multisig');
  const [assets, setAssets] = useState<CoinAsset[]>([]);

  // 组件挂载时加载数据
  useEffect(() => {
    refreshMultisigs();
  }, []);

  useEffect(() => {
    const buildAssets = () => {
      if (!connected || multisigs.length === 0) {
        setAssets([]);
        return;
      }

      try {
        const assets: CoinAsset[] = [];

        // 遍历所有多签账户，构造资产列表
        multisigs.forEach((multisig: any) => {
          // 添加 SOL 资产
          if (multisig.vault.balanceSOL > 0) {
            assets.push({
              symbol: 'SOL',
              icon: 'https://cryptologos.cc/logos/solana-sol-logo.png',
              address: multisig.vault.address,
              balance: multisig.vault.balanceSOL,
              value: multisig.vault.balanceSOL * 200, // 假设 SOL 价格 200 USD
              weight: 1, // 暂时写死权重
            });
          }

          // 添加 Token 资产
          multisig.vault.tokenAccounts.forEach((token: any) => {
            const symbol = getTokenSymbol(token.mint);
            assets.push({
              symbol,
              icon: getTokenIcon(token.mint),
              address: token.address,
              balance: token.uiAmount,
              value: token.uiAmount * getTokenPrice(token.mint), // 假设价格
              weight: 1,
            });
          });
        });

        setAssets(assets);
      } catch (e) {
        message.error('获取资产失败');
      }
    };
    buildAssets();
  }, [connected, multisigs]);

  const columns = [
    {
      title: 'Coin',
      dataIndex: 'symbol',
      key: 'symbol',
      render: (_: any, record: CoinAsset) => (
        <Space>
          <Avatar src={record.icon} size={40} />
          <div>
            <div style={{ fontWeight: 700 }}>{record.symbol}</div>
            <Space size={4}>
              <Text type="secondary" style={{ fontSize: 13 }}>{shortAddr(record.address)}</Text>
              <Button
                type="text"
                size="small"
                icon={<CopyOutlined />}
                onClick={() => {
                  navigator.clipboard.writeText(record.address);
                  message.success('已复制');
                }}
              />
            </Space>
          </div>
        </Space>
      )
    },
    {
      title: 'Balance',
      dataIndex: 'balance',
      key: 'balance',
      render: (v: number) => <span style={{ fontWeight: 700 }}>{v.toFixed(4)}</span>
    },
    {
      title: 'Value',
      dataIndex: 'value',
      key: 'value',
      render: (v: number) => <span style={{ fontWeight: 700 }}>${v.toFixed(2)}</span>
    },
    {
      title: 'Weight',
      dataIndex: 'weight',
      key: 'weight',
      render: (v: number) => (
        <div style={{ minWidth: 80 }}>
          <div style={{ fontWeight: 700 }}>{Math.round(v * 100)}%</div>
          <div style={{ background: '#eee', borderRadius: 4, height: 6, width: 60, marginTop: 2 }}>
            <div style={{ width: `${v * 100}%`, height: '100%', background: '#111' }}></div>
          </div>
        </div>
      )
    }
  ];

  return (
    <div>
      <Table
        columns={columns}
        dataSource={assets}
        rowKey={r => r.symbol + r.address}
        loading={loading}
        pagination={false}
        bordered
      />
    </div>
  );
};

export default AccountPage;