import React, { useState, useCallback, useEffect } from 'react';
import { Table, Button, Tag, message, Space, Switch, Card, Typography } from 'antd';
import { useWallet } from '@solana/wallet-adapter-react';
import { PublicKey, TransactionMessage, VersionedTransaction } from '@solana/web3.js';
import * as multisig from '@sqds/multisig';
import { CheckCircleOutlined, CloseCircleOutlined, ClockCircleOutlined, PlayCircleOutlined } from '@ant-design/icons';
import { useModel } from '@umijs/max';
import Api from '@/services/api';
import { Transaction } from '@/services/types';
import type { ColumnsType } from 'antd/es/table';

const { Text } = Typography;

const TransactionsPage: React.FC = () => {
  const [messageApi, contextHolder] = message.useMessage();
  const { publicKey, signTransaction } = useWallet();
  const { currentMultisig, refreshMultisigs } = useModel('multisig');
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(false);
  const [actionLoading, setActionLoading] = useState<string>('');
  const [useV2Api, setUseV2Api] = useState(false); // 新增：V2 API 开关
  const [vaultAddress, setVaultAddress] = useState<string>(''); // 新增：金库地址
  const [testLoading, setTestLoading] = useState(false); // 测试按钮加载状态

  // 默认配置
  const programId = new PublicKey("SQDS4ep65T869zMMBKyuUq6aD6EgTu8psMjkvj52pCf");

  const handleSuccess = (text: string) => {
    messageApi.success(text);
  };

  const handleError = (text: string) => {
    messageApi.error(text);
  };

  const loadTransactions = useCallback(async () => {
    if (!currentMultisig || !publicKey) return;

    setLoading(true);

    try {
      if (useV2Api) {
        // 使用 V2 API 获取完整转账信息
        const result = await Api.getTransactionsV2(currentMultisig.address);
        setTransactions(result.transactions);
        setVaultAddress(result.vaultAddress);
        handleSuccess(`V2 API: 已加载 ${result.transactions.length} 个交易，金库地址: ${result.vaultAddress.slice(0, 8)}...`);
      } else {
        // 使用 V1 API
        const result = await Api.getTransactions(currentMultisig.address);
        setTransactions(result.transactions);
        setVaultAddress('');
      }
    } catch (error: any) {
      console.error('加载交易失败:', error);
      handleError(error.message || '加载交易失败');
      setTransactions([]);
    } finally {
      setLoading(false);
    }
  }, [currentMultisig, publicKey, useV2Api]);

  // 测试转账信息获取
  const testTransferInfo = useCallback(async () => {
    if (!currentMultisig) return;

    setTestLoading(true);
    try {
      const result = await Api.testTransferInfo(currentMultisig.address);
      if (result.transferInfo) {
        handleSuccess(`找到转账信息: ${result.transferInfo.amount} ${result.transferInfo.type} → ${result.transferInfo.toAddress.slice(0, 8)}...`);
      } else {
        handleError(result.message);
      }
      console.log('测试结果:', result);
    } catch (error: any) {
      console.error('测试失败:', error);
      handleError(error.message || '测试失败');
    } finally {
      setTestLoading(false);
    }
  }, [currentMultisig]);

  useEffect(() => {
    loadTransactions();
  }, [loadTransactions]);

  // Transactions 页面需要最新的交易数据
  useEffect(() => {
    refreshMultisigs(false, 'data-optional'); // 强制刷新，获取最新交易状态
  }, []);

  const handleVote = useCallback(async (transactionIndex: number, vote: 'approve' | 'reject') => {
    if (!signTransaction || !publicKey || !currentMultisig) return;

    try {
      setActionLoading(`${vote}-${transactionIndex}`);

      // 构建投票指令
      const multisigPda = new PublicKey(currentMultisig.address);
      const instruction = vote === 'approve'
        ? multisig.instructions.proposalApprove({
            multisigPda,
            transactionIndex: BigInt(transactionIndex),
            member: publicKey,
            programId,
          })
        : multisig.instructions.proposalReject({
            multisigPda,
            transactionIndex: BigInt(transactionIndex),
            member: publicKey,
            programId,
          });

      // 获取最新区块哈希
      const { blockhash } = await Api.getBlockhash();

      // 创建交易消息
      const message = new TransactionMessage({
        payerKey: publicKey,
        recentBlockhash: blockhash,
        instructions: [instruction],
      });

      // 创建交易
      const transaction = new VersionedTransaction(message.compileToV0Message());
      const signedTransaction = await signTransaction(transaction);

      // 提交投票
      const result = await Api.voteTransaction({
        multisigAddress: currentMultisig.address,
        transactionIndex,
        userPublicKey: publicKey.toBase58(),
        signedTransaction: Buffer.from(signedTransaction.serialize()).toString('base64'),
        vote,
      });

      handleSuccess(result.signature);
      await loadTransactions();

    } catch (error: any) {
      console.error('投票失败:', error);
      handleError(error.message || '投票失败');
    } finally {
      setActionLoading('');
    }
  }, [signTransaction, publicKey, currentMultisig, loadTransactions]);

  const handleExecute = useCallback(async (transactionIndex: number) => {
    if (!signTransaction || !publicKey || !currentMultisig) return;

    try {
      setActionLoading(`execute-${transactionIndex}`);

      // 获取执行指令
      const result = await Api.buildExecuteInstruction({
        multisigAddress: currentMultisig.address,
        transactionIndex,
        executorPublicKey: publicKey.toBase58(),
      });

      // 获取最新区块哈希
      const { blockhash } = await Api.getBlockhash();

      // 转换指令格式
      const instruction = {
        keys: result.instruction.keys.map(key => ({
          pubkey: new PublicKey(key.pubkey),
          isSigner: key.isSigner,
          isWritable: key.isWritable,
        })),
        programId: new PublicKey(result.instruction.programId),
        data: Buffer.from(result.instruction.data),
      };

      // 创建交易消息
      const message = new TransactionMessage({
        payerKey: publicKey,
        recentBlockhash: blockhash,
        instructions: [instruction],
      });

      // 创建交易
      const transaction = new VersionedTransaction(message.compileToV0Message());
      const signedTransaction = await signTransaction(transaction);

      // 执行交易
      const executeResult = await Api.executeTransaction({
        multisigAddress: currentMultisig.address,
        transactionIndex,
        userPublicKey: publicKey.toBase58(),
        signedTransaction: Buffer.from(signedTransaction.serialize()).toString('base64'),
      });

      handleSuccess(executeResult.signature);
      await loadTransactions();

    } catch (error: any) {
      console.error('执行交易失败:', error);
      handleError(error.message || '执行交易失败');
    } finally {
      setActionLoading('');
    }
  }, [signTransaction, publicKey, currentMultisig, loadTransactions]);

  // 格式化时间显示
  const formatTime = (timeString: string | null) => {
    if (!timeString) return '-';
    try {
      const date = new Date(timeString);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
      });
    } catch {
      return '-';
    }
  };

  const formatAddress = (address: string) => {
    return `${address.slice(0, 4)}...${address.slice(-4)}`;
  };

  // 表格列定义
  const columns: ColumnsType<Transaction> = [
    {
      title: '交易ID',
      dataIndex: 'transactionIndex',
      key: 'transactionIndex',
      width: 80,
      render: (index: number) => `#${index}`,
    },
    {
      title: '类型',
      dataIndex: 'transactionType',
      key: 'type',
      width: 80,
      render: (type: string) => {
        switch (type) {
          case 'transfer':
            return '转账';
          default:
            return '未知';
        }
      },
    },
    {
      title: '金额',
      key: 'amount',
      width: 120,
      render: (_, record: Transaction) => {
        if (record.transferAmount && record.transferToken) {
          return `${record.transferAmount} ${record.transferToken}`;
        }
        return '-';
      },
    },
    {
      title: 'From地址',
      dataIndex: 'fromAddress',
      key: 'fromAddress',
      width: 120,
      render: (address: string | null) => {
        if (!address) return '-';
        return formatAddress(address);
      },
    },
    {
      title: 'To地址',
      dataIndex: 'toAddress',
      key: 'toAddress',
      width: 120,
      render: (address: string | null) => {
        if (!address) return '-';
        return formatAddress(address);
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 140,
      render: (time: string | null) => formatTime(time),
    },
    {
      title: '创建者',
      dataIndex: 'creator',
      key: 'creator',
      width: 100,
      render: (creator: string) => formatAddress(creator),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => {
        let color = 'default';
        let text = status;

        switch (status) {
          case 'Active':
            color = 'processing';
            text = '待投票';
            break;
          case 'Approved':
            color = 'success';
            text = '已批准';
            break;
          case 'Rejected':
            color = 'error';
            text = '已拒绝';
            break;
          case 'Executed':
            color = 'success';
            text = '已执行';
            break;
          case 'Cancelled':
            color = 'default';
            text = '已取消';
            break;
        }

        return <Tag color={color}>{text}</Tag>;
      },
    },
    {
      title: '投票进度',
      key: 'progress',
      width: 100,
      render: (_, record: Transaction) => `${record.approvals}/${record.threshold}`,
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      render: (_, record: Transaction) => {
        if (!publicKey) return null;

        if (record.status === 'Active') {
          return (
            <Space>
              <Button
                type="primary"
                size="small"
                loading={actionLoading === `approve-${record.transactionIndex}`}
                onClick={() => handleVote(record.transactionIndex, 'approve')}
                icon={<CheckCircleOutlined />}
              >
                同意
              </Button>
              <Button
                danger
                size="small"
                loading={actionLoading === `reject-${record.transactionIndex}`}
                onClick={() => handleVote(record.transactionIndex, 'reject')}
                icon={<CloseCircleOutlined />}
              >
                拒绝
              </Button>
            </Space>
          );
        }

        if (record.canExecute) {
          return (
            <Button
              type="primary"
              size="small"
              loading={actionLoading === `execute-${record.transactionIndex}`}
              onClick={() => handleExecute(record.transactionIndex)}
              icon={<PlayCircleOutlined />}
            >
              执行
            </Button>
          );
        }

        return <span style={{ color: '#999' }}>无操作</span>;
      },
    },
  ];

  return (
    <>
      {contextHolder}
      <div>
        {/* V2 API 控制面板 */}
        <Card style={{ marginBottom: 16 }}>
          <Space align="center" wrap>
            <Text strong>API 版本:</Text>
            <Switch
              checked={useV2Api}
              onChange={setUseV2Api}
              checkedChildren="V2"
              unCheckedChildren="V1"
            />
            <Text type="secondary">
              {useV2Api ? 'V2: 从金库地址获取完整转账信息' : 'V1: 基础交易信息'}
            </Text>

            <Button
              type="primary"
              size="small"
              loading={testLoading}
              onClick={testTransferInfo}
              style={{ marginLeft: 16 }}
            >
              测试转账信息
            </Button>

            {vaultAddress && (
              <>
                <Text strong style={{ marginLeft: 16 }}>金库地址:</Text>
                <Text code>{vaultAddress.slice(0, 8)}...{vaultAddress.slice(-4)}</Text>
              </>
            )}
          </Space>
        </Card>

        <Table
          columns={columns}
          dataSource={transactions}
          loading={loading}
          rowKey="transactionIndex"
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
          }}
          locale={{
            emptyText: (
              <div style={{ padding: '40px 0', textAlign: 'center', color: '#999' }}>
                <ClockCircleOutlined style={{ fontSize: 48, marginBottom: 16, opacity: 0.3 }} />
                <div>暂无交易记录</div>
              </div>
            ),
          }}
        />
      </div>
    </>
  );
};

export default TransactionsPage;