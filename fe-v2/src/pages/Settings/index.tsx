import React, { useEffect } from 'react'
import { Card, Descriptions, Space } from 'antd'
import { useModel } from '@umijs/max'
import AddressCopy from '@/components/AddressCopy'

const Settings: React.FC = () => {
  const { currentMultisig: multisig, loading, refreshMultisigs } = useModel('multisig')

  useEffect(() => {
    refreshMultisigs()
  }, [])

  return (
    <div>
      <Card loading={loading}>
        <Descriptions bordered>
          <Descriptions.Item label="Members" span={3}>
            <div>{multisig?.members.length}</div>
          </Descriptions.Item>
          <Descriptions.Item label="Threshold" span={3}>
            <Space>{multisig?.threshold}/{multisig?.members?.length ? multisig?.members?.length - 1 : 0}</Space>
          </Descriptions.Item>
          <Descriptions.Item label="Squad Vault" span={3}>
            <AddressCopy address={multisig?.vault.address || ''} label="Squad Vault" showFullAddress={true} />
          </Descriptions.Item>
          <Descriptions.Item label="Multisig Account" span={3}>
            <AddressCopy address={multisig?.address || ''} label="Multisig Account" showFullAddress={true} />
          </Descriptions.Item>
        </Descriptions>
      </Card>
    </div>
  )
}

export default Settings
